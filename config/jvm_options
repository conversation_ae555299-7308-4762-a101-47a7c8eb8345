#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# JVM Heap
-Xms140g
-Xmx140g

# JVM Dump
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/tmp/seatunnel/dump/zeta-server

# Metaspace
-XX:MaxMetaspaceSize=5g

# G1GC
-XX:+UnlockExperimentalVMOptions
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200        # 降低目标暂停时间
-XX:InitiatingHeapOccupancyPercent=45  # 提高触发并发GC的堆占用率阈值
-XX:G1NewSizePercent=10        # 增加新生代最小比例
-XX:G1MaxNewSizePercent=30     # 增加新生代最大比例
-XX:G1HeapRegionSize=32m       # 增加区域大小
-XX:ConcGCThreads=24           # 增加并发GC线程数
-XX:ParallelGCThreads=72       # 增加并行GC线程数
-XX:+ParallelRefProcEnabled    # 启用并行引用处理
-XX:G1RSetUpdatingPauseTimePercent=10  # 减少RSet更新时间
-XX:MaxTenuringThreshold=15    # 提高对象晋升阈值