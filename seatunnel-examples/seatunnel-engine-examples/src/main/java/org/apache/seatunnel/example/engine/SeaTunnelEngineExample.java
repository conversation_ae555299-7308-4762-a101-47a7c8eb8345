/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.example.engine;

import org.apache.seatunnel.core.starter.SeaTunnel;
import org.apache.seatunnel.core.starter.enums.MasterType;
import org.apache.seatunnel.core.starter.exception.CommandException;
import org.apache.seatunnel.core.starter.seatunnel.args.ClientCommandArgs;

import java.io.FileNotFoundException;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Paths;

public class SeaTunnelEngineExample {

    public static void main(String[] args)
            throws FileNotFoundException, URISyntaxException, CommandException {
        // String configurePath = args.length > 0 ? args[0] : "/examples/fake_to_console.conf";
        // String configurePath = args.length > 0 ? args[0] : "/examples/mysql2mysql.conf";//ok
         //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2oracle.conf";//ok
        // String configurePath = args.length > 0 ? args[0] : "/examples/mysql2sqlserver.conf";//ok
        // String configurePath = args.length > 0 ? args[0] : "/examples/mysql2postgresql.conf";//ok
        // String configurePath = args.length > 0 ? args[0] : "/examples/mysql2dm.conf";//ok
        // String configurePath = args.length > 0 ? args[0] :
        // "/examples/cdc/mysql/mysqlcdc.conf";//ok
        // String configurePath = args.length > 0 ? args[0] :
        // "/examples/cdc/oracle/oraclecdc_to_mysql.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/cdc/oracle/00001.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/inceptor_to_mysql.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql_to_mysql1.conf";
        // String configurePath = args.length > 0 ? args[0] :
        // "/examples/cdc/sqlserver/sqlservercdc_to_mysql.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/es2hive.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2es.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2mysql.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2dm.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/hive2es.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2ckfile.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysqlcdc2oracle.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2mysql_pre_post.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/sqlservercdc2dm.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/oraclecdc2dm.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/a.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/b.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2mongo.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mongo2mongo.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2oracle.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2mysql_dynamic_column.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/split.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/11.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/hanna.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2mysql_sqltransform.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/gp.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/oracle2ck.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/decimal.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/loader.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/c.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/d.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/f.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/djbx/ck2mysql.conf";//ok
        //String configurePath = args.length > 0 ? args[0] : "/examples/djbx/ck2oracle.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/djbx/ck2kingbase.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/cdc.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/aa3.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2mysql.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/hive2hbase.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2mysql_delete_or_update.conf";//删除目标端
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2mysql_delete_or_update1.conf";//更新目标端
        //String configurePath = args.length > 0 ? args[0] : "/examples/20240513.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/unionall.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2mysql_delete_or_update2.conf";//更新目标端
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2mysql_delete_or_update2.conf";//更新目标端
        //String configurePath = args.length > 0 ? args[0] : "/examples/fast.conf";//更新目标端
        //String configurePath = args.length > 0 ? args[0] : "/examples/djbx/oracle2kafka.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/djbx/kafka2console.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/kafka2hivethrify.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/djbx/mysql2es.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/djbx/oom.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2kafka.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/kafka2hivethrify.conf";//linux机器可以跑，windows机器跑不了
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2db.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2mysql.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/unionall2.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/unionall_copy.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/error_1.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/join_one_2_many.conf";//left join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/leftjoin.conf";//left join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/rightjoin.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/innerjoin.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/hdfs2mysql.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2mysql.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/mysql2consolehole.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2argoload.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/kafka2many.conf";//right join

        //String configurePath = args.length > 0 ? args[0] : "/examples/error01.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/doris.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/testSFTP.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/gp01.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/0004.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2huawei_oss.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2mysql.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/sqlserver2sqlserver.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2impala.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/0005.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/join_one_2_many_youhua1.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/0006.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/zsp/doris2doris.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2doris.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/unionall_transform.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/error/1.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2doris_test.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/doris_source_and_sink.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/oracle9i2console.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/error/3.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/qihuo/adb2console.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mysql2localfile.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/innerjoin_youhua.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/rightjoin_youhua.conf";//right join
		//String configurePath = args.length > 0 ? args[0] : "/examples/zsp/dm2mysql.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/zsp/dm2mysql.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/kafka2console.conf";//right join
//        String configurePath = args.length > 0 ? args[0] : "/examples/lihj/0007.conf";//right join
        //String configurePath = args.length > 0 ? args[0] : "/examples/zsp/mysql2hive.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/error/7.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/error/test_e_2.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/zsp/mysql2s3.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/010.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/ORACLE_UPCASE2.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/mergefile.conf";

        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/local2console.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/union_key/mysql2mysql.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/union_key/mysql2mysql1.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/union_key/mysql2mysql2.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/union_key/mysql2mysql3.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/union_key/mysql2mysql4.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/union_key/mysql2mysql6.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/cdc/sqlserver/sqlserver2mysql.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/cdc/sqlserver/sqlserver2doris.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/error/kafka2hive.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/metrics/mysql2mysql.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/metrics/mysql2mysql1.conf";
//        String configurePath = args.length > 0 ? args[0] : "/examples/lihj/metrics/mysql2mysql2.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/zsp/mysql2AmazonS3.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/zsp/mysql2AmazonS3icfsdos.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/feture/mysql2localf.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/null/mysql2null.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/null/tdsql2console.conf";
//        String configurePath = args.length > 0 ? args[0] : "/examples/lihj/feture/mysql2localf.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/zsp/mysql2local_20250314.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/zsp/mysql2local_20250314_.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/zsp/mysql2local_20250314_1.conf";
        String configurePath = args.length > 0 ? args[0] : "/examples/lihj/nongxin/gpcopy2localfile.conf";
        //String configurePath = args.length > 0 ? args[0] : "/examples/lihj/nongxin/localfile2mysql.conf";

        String configFile = getTestConfigFile(configurePath);
        ClientCommandArgs clientCommandArgs = new ClientCommandArgs();
        clientCommandArgs.setConfigFile(configFile);
        clientCommandArgs.setCheckConfig(false);
        clientCommandArgs.setJobName(Paths.get(configFile).getFileName().toString());
        // Change Execution Mode to CLUSTER to use client mode, before do this, you should start
        // SeaTunnelEngineServerExample
        clientCommandArgs.setMasterType(MasterType.LOCAL);
        //clientCommandArgs.setMasterType(MasterType.CLUSTER);
        SeaTunnel.run(clientCommandArgs.buildCommand());
    }

    public static String getTestConfigFile(String configFile)
            throws FileNotFoundException, URISyntaxException {
        URL resource = SeaTunnelEngineExample.class.getResource(configFile);
        if (resource == null) {
            throw new FileNotFoundException("Can't find config file: " + configFile);
        }
        return Paths.get(resource.toURI()).toString();
    }
}
