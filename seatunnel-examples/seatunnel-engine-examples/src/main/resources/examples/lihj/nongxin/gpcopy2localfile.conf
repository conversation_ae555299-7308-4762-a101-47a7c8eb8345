env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
	Jdbc {
		url="**********************************************"
		driver="org.postgresql.Driver"
		schema="public"
		user="postgres"
		password="Cdyanfa_123456"
		query="select * from public.emp_quality_millions_100w where 1=1 "
		"fetch_size"="1000"
		"result_table_name"="E000001_source_1"
		"parallelism"=1
		use_copy_source="true"
		copy_format = "CSV_OPTIMIZED"
		use_direct_copy = true
		 pipe_size = 52428800
		  copy_buffer_size = 16384
	}
}
transform {
}
sink {
 LocalFile {
       path="d://tmp"
       tmp_path="d://tmp1"
       file_format_type="csv"
       field_delimiter=","
       row_delimiter="\n"
   }
}