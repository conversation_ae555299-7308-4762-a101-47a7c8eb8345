package org.apache.seatunnel.connectors.seatunnel.console.hole.sink;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.seatunnel.api.sink.SinkWriter;
import org.apache.seatunnel.api.sink.SupportMultiTableSinkWriter;
import org.apache.seatunnel.api.table.event.SchemaChangeEvent;
import org.apache.seatunnel.api.table.event.handler.DataTypeChangeEventDispatcher;
import org.apache.seatunnel.api.table.event.handler.DataTypeChangeEventHandler;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.common.utils.JsonUtils;
import org.apache.seatunnel.common.utils.SeaTunnelException;
import org.apache.seatunnel.connectors.seatunnel.common.sink.AbstractSinkWriter;

import java.io.IOException;
import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2024/6/5
 */
@Slf4j
public class ConsoleHoleSinkWriter extends AbstractSinkWriter<SeaTunnelRow, Void>
        implements SupportMultiTableSinkWriter<Void> {

    private SeaTunnelRowType seaTunnelRowType;
    private final AtomicLong rowCounter = new AtomicLong(0);
    private final SinkWriter.Context context;
    private final DataTypeChangeEventHandler dataTypeChangeEventHandler;

    boolean isPrintData = true;
    int delayMs = 0;

    public ConsoleHoleSinkWriter(SeaTunnelRowType seaTunnelRowType,
                                 SinkWriter.Context context,
                                 boolean isPrintData,
                                 int delayMs) {
        this.seaTunnelRowType = seaTunnelRowType;
        this.context = context;
        this.isPrintData = isPrintData;
        this.delayMs = delayMs;
        this.dataTypeChangeEventHandler = new DataTypeChangeEventDispatcher();
        //log.info("output rowType: {}", fieldsInfo(seaTunnelRowType));
    }

    @Override
    public void applySchemaChange(SchemaChangeEvent event) throws IOException {
        seaTunnelRowType = dataTypeChangeEventHandler.reset(seaTunnelRowType).apply(event);
    }

    @Override
    public void write(SeaTunnelRow element) throws IOException {
        int bytesSize =element.getBytesSize();
        boolean flag = Arrays.stream(element.getFields()).allMatch(field -> field==null);
        if(bytesSize==0 && flag){
            return;
        }
        if (delayMs > 0) {
            try {
                Thread.sleep(delayMs);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new SeaTunnelException(e);
            }
        }
    }

    @Override
    public void close() throws IOException {

    }
}
