/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.copy;

import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.psql.PostgresDialect;
import org.apache.seatunnel.connectors.seatunnel.jdbc.config.JdbcSourceConfig;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.postgresql.copy.CopyManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Iterator;
import java.util.ArrayList;
import java.util.List;

/**
 * 优化的PostgreSQL COPY数据读取器
 * 直接使用CopyManager的OutputStream，避免管道流的开销
 */
public class OptimizedCopyReader implements Closeable {
    
    private static final Logger LOG = LoggerFactory.getLogger(OptimizedCopyReader.class);
    
    private final CopyManager copyManager;
    private final String copySql;
    private final SeaTunnelRowType rowType;
    private final JdbcSourceConfig config;
    private final PostgresDialect.CopyFormat copyFormat;
    
    private InputStream copyStream;
    private CSVParser csvParser;
    private Iterator<CSVRecord> recordIterator;
    private BufferedInputStream bufferedStream;

    // 批量处理相关字段
    private List<SeaTunnelRow> batchBuffer;
    private int batchIndex;
    private static final int BATCH_SIZE = 1000; // 批量处理大小
    
    public OptimizedCopyReader(CopyManager copyManager, String copySql,
                              SeaTunnelRowType rowType, JdbcSourceConfig config) {
        this.copyManager = copyManager;
        this.copySql = copySql;
        this.rowType = rowType;
        this.config = config;
        this.copyFormat = PostgresDialect.CopyFormat.valueOf(config.getCopyFormat());
        this.batchBuffer = new ArrayList<>(BATCH_SIZE);
        this.batchIndex = 0;
    }
    
    public void open() throws IOException {
        try {
            if (config.isUseDirectCopy()) {
                // 直接使用CopyManager的输出流，避免管道流
                openDirectCopy();
            } else {
                // 使用传统的管道流方式（兼容性）
                openPipedCopy();
            }
        } catch (Exception e) {
            throw new IOException("Failed to open COPY stream", e);
        }
    }
    
    private void openDirectCopy() throws Exception {
        // 使用流式处理，避免将所有数据加载到内存
        // 创建一个管道，但使用更大的缓冲区和优化的处理
        PipedOutputStream out = new PipedOutputStream();
        PipedInputStream in = new PipedInputStream(out, config.getPipeSize());

        // 在单独线程中执行COPY，但优化线程处理
        Thread copyThread = new Thread(() -> {
            try {
                copyManager.copyOut(copySql, out);
            } catch (Exception e) {
                LOG.error("COPY operation failed", e);
                throw new RuntimeException(e);
            } finally {
                try {
                    out.close();
                } catch (IOException ignore) {}
            }
        }, "optimized-pg-copy-thread");

        // 设置线程优先级以提高性能
        copyThread.setPriority(Thread.MAX_PRIORITY);
        copyThread.start();

        copyStream = in;
        initializeParser();
    }
    
    private void openPipedCopy() throws Exception {
        // 传统的管道流方式，但使用更大的缓冲区
        PipedOutputStream out = new PipedOutputStream();
        PipedInputStream in = new PipedInputStream(out, config.getPipeSize());
        
        // 在单独线程中执行COPY
        Thread copyThread = new Thread(() -> {
            try {
                copyManager.copyOut(copySql, out);
            } catch (Exception e) {
                LOG.error("COPY operation failed", e);
                throw new RuntimeException(e);
            } finally {
                try {
                    out.close();
                } catch (IOException ignore) {}
            }
        }, "pg-copy-thread");
        
        copyThread.start();
        copyStream = in;
        
        initializeParser();
    }
    
    private void initializeParser() throws IOException {
        // 使用缓冲流提高读取性能
        bufferedStream = new BufferedInputStream(copyStream, config.getCopyBufferSize());
        
        if (copyFormat == PostgresDialect.CopyFormat.BINARY) {
            // TODO: 实现二进制格式解析
            throw new UnsupportedOperationException("Binary format not yet implemented");
        } else {
            // CSV格式解析
            Reader reader = new InputStreamReader(bufferedStream, StandardCharsets.UTF_8);
            
            CSVFormat format;
            if (copyFormat == PostgresDialect.CopyFormat.CSV_OPTIMIZED) {
                // 优化的CSV格式：TAB分隔符，减少引号处理
                format = CSVFormat.DEFAULT
                    .withDelimiter('\t')
                    .withQuote('\b')  // 使用不常见字符作为引号
                    .withEscape('\b')
                    .withNullString("")
                    .withIgnoreEmptyLines(false)
                    .withTrim(false);
            } else {
                // 标准CSV格式
                format = CSVFormat.DEFAULT
                    .withDelimiter(',')
                    .withNullString("")
                    .withIgnoreEmptyLines(false)
                    .withTrim(false);
            }
            
            csvParser = format.parse(reader);
            recordIterator = csvParser.iterator();
        }
    }
    
    public boolean hasNext() {
        // 检查批量缓冲区是否还有数据
        if (batchIndex < batchBuffer.size()) {
            return true;
        }

        // 检查是否还有更多数据可以读取
        return recordIterator != null && recordIterator.hasNext();
    }
    
    public SeaTunnelRow nextRecord() {
        // 检查批量缓冲区
        if (batchIndex < batchBuffer.size()) {
            return batchBuffer.get(batchIndex++);
        }

        // 批量缓冲区为空，尝试填充
        if (!fillBatchBuffer()) {
            return null;
        }

        // 返回批量缓冲区中的第一条记录
        batchIndex = 1;
        return batchBuffer.get(0);
    }

    private boolean fillBatchBuffer() {
        batchBuffer.clear();
        batchIndex = 0;

        try {
            // 批量读取记录
            int count = 0;
            while (count < BATCH_SIZE && recordIterator != null && recordIterator.hasNext()) {
                CSVRecord record = recordIterator.next();
                SeaTunnelRow row = convertCsvRecordToRow(record);
                batchBuffer.add(row);
                count++;
            }

            return !batchBuffer.isEmpty();
        } catch (Exception e) {
            throw new RuntimeException("Failed to fill batch buffer", e);
        }
    }
    
    private SeaTunnelRow convertCsvRecordToRow(CSVRecord record) {
        Object[] fields = new Object[rowType.getTotalFields()];
        
        for (int i = 0; i < fields.length && i < record.size(); i++) {
            String value = record.get(i);
            SeaTunnelDataType<?> type = rowType.getFieldType(i);
            fields[i] = parseValueByType(value, type);
        }
        
        return new SeaTunnelRow(fields);
    }
    
    private Object parseValueByType(String value, SeaTunnelDataType<?> type) {
        if (value == null || value.isEmpty()) {
            return null;
        }

        try {
            // 优化的类型转换，减少字符串操作
            switch (type.getSqlType()) {
                case STRING:
                    return value;
                case BOOLEAN:
                    // 优化布尔值解析
                    return "t".equals(value) || "true".equalsIgnoreCase(value) || "1".equals(value);
                case TINYINT:
                    return Byte.parseByte(value);
                case SMALLINT:
                    return Short.parseShort(value);
                case INT:
                    return Integer.parseInt(value);
                case BIGINT:
                    return Long.parseLong(value);
                case FLOAT:
                    return Float.parseFloat(value);
                case DOUBLE:
                    return Double.parseDouble(value);
                case DECIMAL:
                    return new BigDecimal(value);
                case DATE:
                    return parseDate(value);
                case TIME:
                    return parseTime(value);
                case TIMESTAMP:
                    return parseTimestamp(value);
                case BYTES:
                    return value.getBytes(StandardCharsets.UTF_8);
                default:
                    return value;
            }
        } catch (Exception e) {
            LOG.warn("Failed to parse value '{}' as type {}, returning as string", value, type, e);
            return value;
        }
    }

    // 优化的日期时间解析方法
    private LocalDate parseDate(String value) {
        try {
            return LocalDate.parse(value);
        } catch (Exception e) {
            // 尝试其他格式
            if (value.contains("/")) {
                // 处理 MM/dd/yyyy 格式
                String[] parts = value.split("/");
                if (parts.length == 3) {
                    return LocalDate.of(Integer.parseInt(parts[2]),
                                      Integer.parseInt(parts[0]),
                                      Integer.parseInt(parts[1]));
                }
            }
            throw e;
        }
    }

    private LocalTime parseTime(String value) {
        try {
            return LocalTime.parse(value);
        } catch (Exception e) {
            // 处理不同的时间格式
            if (value.length() == 8 && value.contains(":")) {
                return LocalTime.parse(value);
            }
            throw e;
        }
    }

    private LocalDateTime parseTimestamp(String value) {
        try {
            // 优化时间戳解析，避免字符串替换
            if (value.contains(" ")) {
                int spaceIndex = value.indexOf(' ');
                String datePart = value.substring(0, spaceIndex);
                String timePart = value.substring(spaceIndex + 1);
                return LocalDateTime.of(LocalDate.parse(datePart), LocalTime.parse(timePart));
            } else {
                return LocalDateTime.parse(value);
            }
        } catch (Exception e) {
            // 回退到原始方法
            return LocalDateTime.parse(value.replace(' ', 'T'));
        }
    }
    
    @Override
    public void close() throws IOException {
        if (csvParser != null) {
            csvParser.close();
        }
        if (bufferedStream != null) {
            bufferedStream.close();
        }
        if (copyStream != null) {
            copyStream.close();
        }
    }
}
