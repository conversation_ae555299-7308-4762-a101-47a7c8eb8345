/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.internal;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TablePath;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.RowKind;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.common.exception.CommonErrorCodeDeprecated;
import org.apache.seatunnel.connectors.seatunnel.jdbc.config.JdbcSourceConfig;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorErrorCode;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorException;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.connection.JdbcConnectionProvider;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.connection.SimpleJdbcConnectionProvider;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.converter.JdbcRowConverter;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.JdbcDialect;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.JdbcDialectLoader;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.psql.PostgresDialect;
import org.apache.seatunnel.connectors.seatunnel.jdbc.source.ChunkSplitter;
import org.apache.seatunnel.connectors.seatunnel.jdbc.source.JdbcSourceSplit;
import org.postgresql.PGConnection;
import org.postgresql.copy.CopyManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PipedInputStream;
import java.io.PipedOutputStream;
import java.io.Reader;
import java.io.Serializable;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Iterator;
import java.util.Map;

/**
 * InputFormat to read data from a database and generate Rows. The InputFormat has to be configured
 * using the supplied InputFormatBuilder. A valid RowTypeInfo must be properly configured in the
 * builder
 */
public class JdbcInputFormat implements Serializable {

    private static final long serialVersionUID = 2L;
    private static final Logger LOG = LoggerFactory.getLogger(JdbcInputFormat.class);

    private final JdbcDialect jdbcDialect;
    private final JdbcRowConverter jdbcRowConverter;
    private final Map<TablePath, CatalogTable> tables;
    private final ChunkSplitter chunkSplitter;

    private transient String splitTableId;
    private transient TableSchema splitTableSchema;
    private transient PreparedStatement statement;
    private transient ResultSet resultSet;
    private volatile boolean hasNext;
    private JdbcSourceConfig sourceConfig;
    private boolean flag = false;
    private transient CSVParser copyCsvParser;
    private transient Iterator<CSVRecord> copyRecordIterator;
    private transient CopyManager copyManager;
    private transient InputStream copyInputStream;
    private boolean useCopyRead = false;
    private transient Thread copyThread;


    public JdbcInputFormat(JdbcSourceConfig config, Map<TablePath, CatalogTable> tables) {
        this.jdbcDialect =
                JdbcDialectLoader.load(
                        config.getJdbcConnectionConfig().getUrl(), config.getCompatibleMode());
        this.chunkSplitter = ChunkSplitter.create(config);
        this.jdbcRowConverter = jdbcDialect.getRowConverter();
        this.tables = tables;
        this.sourceConfig = config;
        this.flag = config.isEmptyDataStrategy();
        this.useCopyRead = config.isUseCopyRead();
    }

    public void openInputFormat() {
        if (CollectionUtils.isNotEmpty(sourceConfig.getPreSQL())) {
            try {
                JdbcConnectionProvider jdbcConnectionProvider =
                        new SimpleJdbcConnectionProvider(
                                sourceConfig.getJdbcConnectionConfig());
                Connection conn = jdbcConnectionProvider.getOrEstablishConnection();
                try (Statement stat = conn.createStatement()) {
                    for (String preSql : sourceConfig.getPreSQL()) {
                        stat.execute(preSql);
                    }
                    if (!conn.getAutoCommit()) {
                        conn.commit();
                    }
                } catch (SQLException e) {
                    if (!conn.getAutoCommit()) {
                        conn.rollback();
                    }
                }
            } catch (Exception e) {
                throw new JdbcConnectorException(
                        JdbcConnectorErrorCode.PRE_SQL_FAILED,
                        "Execute preSql failed." + e.getMessage(),
                        e);
            }
        }
    }

    public void closeInputFormat() throws IOException {
        if (CollectionUtils.isNotEmpty(sourceConfig.getPostSQL())) {
            try {
                JdbcConnectionProvider jdbcConnectionProvider =
                        new SimpleJdbcConnectionProvider(
                                sourceConfig.getJdbcConnectionConfig());
                Connection conn = jdbcConnectionProvider.getOrEstablishConnection();
                try (Statement stat = conn.createStatement()) {
                    for (String postSql : sourceConfig.getPostSQL()) {
                        stat.execute(postSql);
                    }
                    if (!conn.getAutoCommit()) {
                        conn.commit();
                    }
                } catch (SQLException e) {
                    if (!conn.getAutoCommit()) {
                        conn.rollback();
                    }
                }
            } catch (SQLException | ClassNotFoundException e) {
                throw new JdbcConnectorException(JdbcConnectorErrorCode.POST_SQL_FAILED, "Execute postSQL failed", e);
            }
        }
        close();
        if (chunkSplitter != null) {
            chunkSplitter.close();
        }
    }

    /**
     * Connects to the source database and executes the query
     *
     * @param inputSplit which is ignored if this InputFormat is executed as a non-parallel source,
     *                   a "hook" to the query parameters otherwise (using its <i>parameterId</i>)
     * @throws IOException if there's an error during the execution of the query
     */
    public void open(JdbcSourceSplit inputSplit) throws IOException {
        if(useCopyRead){
            try {
                splitTableSchema = tables.get(inputSplit.getTablePath()).getTableSchema();
                splitTableId = inputSplit.getTablePath().toString();

                JdbcConnectionProvider provider = new SimpleJdbcConnectionProvider(sourceConfig.getJdbcConnectionConfig());
                Connection conn = provider.getOrEstablishConnection();
                CopyManager copyManager = conn.unwrap(PGConnection.class).getCopyAPI();

                String query = inputSplit.getSplitQuery();
                if (org.apache.commons.lang3.StringUtils.isEmpty(query)) {
                    query = String.format("SELECT * FROM %s", jdbcDialect.tableIdentifier(inputSplit.getTablePath()));
                }
                String copySql = ((PostgresDialect) jdbcDialect).getCopySelectSql(query);
                //String copySql = "COPY emp_quality_millions_100w TO STDOUT WITH (FORMAT CSV, DELIMITER ',', NULL '', HEADER)";

                // 用管道流实现copyOut到InputStream
                PipedOutputStream out = new PipedOutputStream();
                copyInputStream = new PipedInputStream(out, 1024*1024*10);

                copyThread = new Thread(() -> {
                    try {
                        copyManager.copyOut(copySql, out);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        try {
                            out.close();
                        } catch (IOException ignore) {

                        }
                    }
                }, "pg-copy-thread");
                copyThread.start();

                Reader reader = new InputStreamReader(copyInputStream, StandardCharsets.UTF_8);
                copyCsvParser = CSVFormat.DEFAULT.parse(reader);
                copyRecordIterator = copyCsvParser.iterator();
                hasNext = copyRecordIterator.hasNext();
            } catch (Exception e) {
                throw new JdbcConnectorException(
                        JdbcConnectorErrorCode.CONNECT_DATABASE_FAILED,
                        "open() failed (copy mode): " + e.getMessage(),
                        e
                );
            }
        }else{
            try {
                splitTableSchema = tables.get(inputSplit.getTablePath()).getTableSchema();
                splitTableId = inputSplit.getTablePath().toString();

                statement = chunkSplitter.generateSplitStatement(inputSplit);
                resultSet = statement.executeQuery();
                hasNext = resultSet.next();
                if (!hasNext && flag) {
                    hasNext = true;
                }
            } catch (SQLException se) {
                throw new JdbcConnectorException(
                        JdbcConnectorErrorCode.CONNECT_DATABASE_FAILED,
                        "open() failed." + se.getMessage(),
                        se);
            }
        }

    }

    /**
     * Closes all resources used.
     *
     * @throws IOException Indicates that a resource could not be closed.
     */
    public void close() throws IOException {
        if(useCopyRead){
            if (copyCsvParser != null) copyCsvParser.close();
            if (copyInputStream != null) copyInputStream.close();
            if (copyThread != null) {
                try { copyThread.join(1000); } catch (InterruptedException ignore) {}
            }
        }else{
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    LOG.info("ResultSet couldn't be closed - " + e.getMessage());
                }
            }
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    LOG.info("Statement couldn't be closed - " + e.getMessage());
                }
            }
        }
    }

    /**
     * Checks whether all data has been read.
     *
     * @return boolean value indication whether all data has been read.
     */
    public boolean reachedEnd() {
        return !hasNext;
    }

    /**
     * Convert a row of data to seatunnelRow
     */
    public SeaTunnelRow nextRecord() {
        if(useCopyRead){
            if (!hasNext) {
                return null;
            }
            try {
                CSVRecord record = copyRecordIterator.next();
                SeaTunnelRow row = convertCsvRecordToRow(record, splitTableSchema);
                row.setTableId(splitTableId);
                row.setRowKind(RowKind.INSERT);
                hasNext = copyRecordIterator.hasNext();
                return row;
            } catch (Exception e) {
                throw new JdbcConnectorException(
                        CommonErrorCodeDeprecated.SQL_OPERATION_FAILED,
                        "Couldn't read data from copy stream - " + e.getMessage(),
                        e
                );
            }
        }else{
            try {
                if (!hasNext) {
                    return null;
                }
                SeaTunnelRow seaTunnelRow = jdbcRowConverter.toInternal(resultSet, splitTableSchema,flag);
                seaTunnelRow.setTableId(splitTableId);
                seaTunnelRow.setRowKind(RowKind.INSERT);

                // update hasNext after we've read the record
                //hasNext = resultSet.next();
                //20241224 flag用于标识源表是否为空，为空时db2重复调用resultSet.next();方法会报resultSet已关闭的错，测试结论是db2驱动中返回false时就会自动关闭resultSet
                if (!flag) {
                    hasNext = resultSet.next();
                } else {
                    hasNext = false;
                }
                return seaTunnelRow;
            } catch (SQLException se) {
                throw new JdbcConnectorException(
                        CommonErrorCodeDeprecated.SQL_OPERATION_FAILED,
                        "Couldn't read data - " + se.getMessage(),
                        se);
            } catch (NullPointerException npe) {
                throw new JdbcConnectorException(
                        CommonErrorCodeDeprecated.SQL_OPERATION_FAILED,
                        "Couldn't access resultSet",
                        npe);
            }
        }
    }
    private SeaTunnelRow convertCsvRecordToRow(CSVRecord record, TableSchema tableSchema) {
        SeaTunnelRowType typeInfo = tableSchema.toPhysicalRowDataType();
        Object[] fields = new Object[typeInfo.getTotalFields()];
        for (int i = 0; i < fields.length; i++) {
            String value = record.get(i);
            SeaTunnelDataType<?> type = typeInfo.getFieldType(i);
            fields[i] = parseValueByType(value, type);
        }
        return new SeaTunnelRow(fields);
    }

    private Object parseValueByType(String value, SeaTunnelDataType<?> type) {
        if (value == null || value.isEmpty()) return null;
        switch (type.getSqlType()) {
            case STRING: return value;
            case BOOLEAN: return Boolean.parseBoolean(value);
            case TINYINT: return Byte.parseByte(value);
            case SMALLINT: return Short.parseShort(value);
            case INT: return Integer.parseInt(value);
            case BIGINT: return Long.parseLong(value);
            case FLOAT: return Float.parseFloat(value);
            case DOUBLE: return Double.parseDouble(value);
            case DECIMAL: return new BigDecimal(value);
            case DATE: return LocalDate.parse(value);
            case TIME: return LocalTime.parse(value);
            case TIMESTAMP: return LocalDateTime.parse(value.replace(' ', 'T'));
            case BYTES: return value.getBytes(StandardCharsets.UTF_8);
            default: throw new UnsupportedOperationException("Unsupported type: " + type);
        }
    }
}
