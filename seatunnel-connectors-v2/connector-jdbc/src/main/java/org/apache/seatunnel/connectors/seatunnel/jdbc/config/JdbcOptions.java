/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.config;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.api.sink.DataSaveMode;
import org.apache.seatunnel.api.sink.SchemaSaveMode;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.dialectenum.FieldIdeEnum;
import org.apache.seatunnel.connectors.seatunnel.jdbc.utils.ConflictStrategyEnum;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@SuppressWarnings("checkstyle:MagicNumber")
public interface JdbcOptions {

    Option<String> URL = Options.key("url").stringType().noDefaultValue().withDescription("url");

    Option<String> DRIVER =
            Options.key("driver").stringType().noDefaultValue().withDescription("driver");

    Option<Integer> CONNECTION_CHECK_TIMEOUT_SEC =
            Options.key("connection_check_timeout_sec")
                    .intType()
                    .defaultValue(30)
                    .withDescription("connection check time second");
    Option<String> COMPATIBLE_MODE =
            Options.key("compatible_mode")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "The compatible mode of database, required when the database supports multiple compatible modes. For example, when using OceanBase database, you need to set it to 'mysql' or 'oracle'.");

    Option<Integer> MAX_RETRIES =
            Options.key("max_retries").intType().defaultValue(0).withDescription("max_retired");

    Option<String> USER = Options.key("user").stringType().noDefaultValue().withDescription("user");

    Option<String> PASSWORD =
            Options.key("password").stringType().noDefaultValue().withDescription("password");

    Option<String> QUERY =
            Options.key("query").stringType().noDefaultValue().withDescription("query");

    Option<SchemaSaveMode> SCHEMA_SAVE_MODE =
            Options.key("schema_save_mode")
                    .enumType(SchemaSaveMode.class)
                    .defaultValue(SchemaSaveMode.CREATE_SCHEMA_WHEN_NOT_EXIST)
                    .withDescription("schema_save_mode");
    Option<DataSaveMode> DATA_SAVE_MODE =
            Options.key("data_save_mode")
                    .enumType(DataSaveMode.class)
                    .defaultValue(DataSaveMode.APPEND_DATA)
                    .withDescription("data_save_mode");

    Option<String> CUSTOM_SQL =
            Options.key("custom_sql").stringType().noDefaultValue().withDescription("custom_sql");

    Option<Boolean> AUTO_COMMIT =
            Options.key("auto_commit")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription("auto commit");

    Option<Integer> BATCH_SIZE =
            Options.key("batch_size").intType().defaultValue(1000).withDescription("batch size");

    Option<Integer> FETCH_SIZE =
            Options.key("fetch_size")
                    .intType()
                    .defaultValue(0)
                    .withDescription(
                            "For queries that return a large number of objects, "
                                    + "you can configure the row fetch size used in the query to improve performance by reducing the number database hits required to satisfy the selection criteria. Zero means use jdbc default value.");

    Option<Boolean> IS_EXACTLY_ONCE =
            Options.key("is_exactly_once")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("exactly once");

    Option<Boolean> GENERATE_SINK_SQL =
            Options.key("generate_sink_sql")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("generate sql using the database table");

    Option<String> XA_DATA_SOURCE_CLASS_NAME =
            Options.key("xa_data_source_class_name")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("data source class name");

    Option<Integer> MAX_COMMIT_ATTEMPTS =
            Options.key("max_commit_attempts")
                    .intType()
                    .defaultValue(3)
                    .withDescription("max commit attempts");

    Option<Integer> TRANSACTION_TIMEOUT_SEC =
            Options.key("transaction_timeout_sec")
                    .intType()
                    .defaultValue(-1)
                    .withDescription("transaction timeout (second)");

    Option<String> DATABASE =
            Options.key("database").stringType().noDefaultValue().withDescription("database");

    Option<String> TABLE =
            Options.key("table").stringType().noDefaultValue().withDescription("table");

    Option<List<String>> PRIMARY_KEYS =
            Options.key("primary_keys").listType().noDefaultValue().withDescription("primary keys");

    Option<Boolean> SUPPORT_UPSERT_BY_QUERY_PRIMARY_KEY_EXIST =
            Options.key("support_upsert_by_query_primary_key_exist")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("support upsert by query primary_key exist");

    Option<Boolean> ENABLE_UPSERT =
            Options.key("enable_upsert")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription("enable upsert by primary_keys exist");
    Option<Boolean> IS_PRIMARY_KEY_UPDATED =
            Options.key("is_primary_key_updated")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription(
                            "is the primary key updated when performing an update operation");
    Option<Boolean> SUPPORT_UPSERT_BY_INSERT_ONLY =
            Options.key("support_upsert_by_insert_only")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("support upsert by insert only");

    Option<Boolean> USE_COPY_STATEMENT =
            Options.key("use_copy_statement")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("support copy in statement (postgresql)");

    /**
     * source config
     */
    Option<String> PARTITION_COLUMN =
            Options.key("partition_column")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("partition column");

    Option<List<String>> PRE_SQL = Options.key("pre_sql")
            .listType()
            .noDefaultValue()
            .withDescription("previous sql before sink");

    Option<List<String>> POST_SQL = Options.key("post_sql")
            .listType()
            .noDefaultValue()
            .withDescription("post sql after sink");

    Option<BigDecimal> PARTITION_UPPER_BOUND =
            Options.key("partition_upper_bound")
                    .bigDecimalType()
                    .noDefaultValue()
                    .withDescription("partition upper bound");
    Option<BigDecimal> PARTITION_LOWER_BOUND =
            Options.key("partition_lower_bound")
                    .bigDecimalType()
                    .noDefaultValue()
                    .withDescription("partition lower bound");
    Option<Integer> PARTITION_NUM =
            Options.key("partition_num")
                    .intType()
                    .noDefaultValue()
                    .withDescription("partition num");

    Option<FieldIdeEnum> FIELD_IDE =
            Options.key("field_ide")
                    .enumType(FieldIdeEnum.class)
                    .noDefaultValue()
                    .withDescription("Whether case conversion is required");

    Option<Boolean> USE_KERBEROS =
            Options.key("use_kerberos")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("Whether to enable Kerberos, default is false.");

    Option<String> KERBEROS_PRINCIPAL =
            Options.key("kerberos_principal")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "When use kerberos, we should set kerberos principal such as 'test_user@xxx'. ");

    Option<String> KERBEROS_KEYTAB_PATH =
            Options.key("kerberos_keytab_path")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "When use kerberos, we should set kerberos principal file path such as '/home/<USER>/test_user.keytab'. ");

    Option<String> KRB5_PATH =
            Options.key("krb5_path")
                    .stringType()
                    .defaultValue("/etc/krb5.conf")
                    .withDescription(
                            "When use kerberos, we should set krb5 path file path such as '/seatunnel/krb5.conf' or use the default path '/etc/krb5.conf");

    Option<Map<String, String>> PROPERTIES =
            Options.key("properties")
                    .mapType()
                    .noDefaultValue()
                    .withDescription("additional connection configuration parameters");

    Option<String> PK_STRATEGY =
            Options.key("pk_strategy")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "主键重复策略,默认是停止");
    Option<String> INSERT_ERROR_STRATEGY =
            Options.key("insert_error_strategy")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "主键重复策略,默认是停止");
    Option<String> CONFLICT_STRATEGY_ROW =
            Options.key("conflict_strategy_row")
                    .stringType()
                    .defaultValue(ConflictStrategyEnum.NONE.getValue())
                    .withDescription(
                            "冲突策略");

    Option<Boolean> EMPTY_DATA_STRATEGY = Options.key("empty_data_strategy")
            .booleanType()
            .defaultValue(false)
            .withDescription("空数据执行策略，默认源端为空，不走transform和sink的");

    Option<Boolean> USE_COPY_SOURCE =
            Options.key("use_copy_source")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("Whether to use PostgreSQL COPY TO STDOUT for source reading");
    Option<Integer> PIPE_SIZE =
            Options.key("pipe_size")
                    .intType()
                    .defaultValue(1024*1024*50)  // 增大默认缓冲区到50MB
                    .withDescription("Buffer size for PostgreSQL COPY TO STDOUT pipe in bytes");
    Option<String> COPY_FORMAT =
            Options.key("copy_format")
                    .stringType()
                    .defaultValue("CSV_OPTIMIZED")
                    .withDescription("PostgreSQL COPY format: CSV, CSV_OPTIMIZED, or BINARY");
    Option<Integer> COPY_BUFFER_SIZE =
            Options.key("copy_buffer_size")
                    .intType()
                    .defaultValue(8192)
                    .withDescription("Buffer size for reading COPY data stream");
    Option<Boolean> USE_DIRECT_COPY =
            Options.key("use_direct_copy")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription("Use direct copy stream without pipe (better performance)");

/*    Option<Boolean> ONLY_DELETE_CONFLICTING_ROWS =
            Options.key("only_delete_conflicting_rows")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription(
                            "是否只删除冲突行,默认是false");
    Option<Boolean> ONLY_UPDATE_CONFLICTING_ROWS =
            Options.key("only_update_conflicting_rows")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription(
                            "是否更新冲突行,默认是false");
    Option<Boolean> DELETE_CONFLICTING_BEFORE_INSERTING_ROWS =
            Options.key("delete_conflicting_before_inserting_rows")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription(
                            "是否先删除,后插入,默认是false");*/

}
