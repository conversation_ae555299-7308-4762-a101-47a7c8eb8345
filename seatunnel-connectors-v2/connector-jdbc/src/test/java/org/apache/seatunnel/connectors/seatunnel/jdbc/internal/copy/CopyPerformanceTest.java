/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.copy;

import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.api.table.type.BasicType;
import org.apache.seatunnel.connectors.seatunnel.jdbc.config.JdbcSourceConfig;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.psql.PostgresDialect;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import org.postgresql.copy.CopyManager;
import org.postgresql.PGConnection;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.concurrent.atomic.AtomicLong;

/**
 * PostgreSQL COPY性能测试
 * 
 * 注意：此测试需要真实的PostgreSQL数据库连接，默认禁用
 * 要运行此测试，请：
 * 1. 移除@Disabled注解
 * 2. 配置正确的数据库连接参数
 * 3. 确保测试表存在并包含足够的数据
 */
@Disabled("需要真实数据库连接")
public class CopyPerformanceTest {
    
    // 测试配置 - 请根据实际环境修改
    private static final String DB_URL = "***************************************";
    private static final String DB_USER = "testuser";
    private static final String DB_PASSWORD = "testpass";
    private static final String TEST_TABLE = "performance_test_table";
    private static final String TEST_QUERY = "SELECT * FROM " + TEST_TABLE + " LIMIT 100000";
    
    @Test
    public void testCopyPerformance() throws Exception {
        System.out.println("开始PostgreSQL COPY性能测试...");
        
        // 测试不同的COPY格式
        testCopyFormat(PostgresDialect.CopyFormat.CSV, "标准CSV格式");
        testCopyFormat(PostgresDialect.CopyFormat.CSV_OPTIMIZED, "优化CSV格式");
        
        // 测试不同的配置
        testDifferentConfigurations();
    }
    
    private void testCopyFormat(PostgresDialect.CopyFormat format, String description) throws Exception {
        System.out.println("\n=== 测试 " + description + " ===");
        
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            CopyManager copyManager = conn.unwrap(PGConnection.class).getCopyAPI();
            
            // 创建测试配置
            JdbcSourceConfig config = createTestConfig(format);
            
            // 创建行类型定义
            SeaTunnelRowType rowType = createTestRowType();
            
            // 生成COPY SQL
            PostgresDialect dialect = new PostgresDialect();
            String copySql = dialect.getCopySelectSql(TEST_QUERY, format);
            
            System.out.println("COPY SQL: " + copySql);
            
            // 执行性能测试
            long startTime = System.currentTimeMillis();
            AtomicLong recordCount = new AtomicLong(0);
            
            try (OptimizedCopyReader reader = new OptimizedCopyReader(copyManager, copySql, rowType, config)) {
                reader.open();
                
                while (reader.hasNext()) {
                    SeaTunnelRow row = reader.nextRecord();
                    if (row != null) {
                        recordCount.incrementAndGet();
                    }
                }
            }
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            long totalRecords = recordCount.get();
            double recordsPerSecond = (double) totalRecords / (duration / 1000.0);
            
            System.out.println("结果统计:");
            System.out.println("  总记录数: " + totalRecords);
            System.out.println("  总耗时: " + duration + " ms");
            System.out.println("  处理速度: " + String.format("%.2f", recordsPerSecond) + " 记录/秒");
            System.out.println("  内存使用: " + getMemoryUsage() + " MB");
        }
    }
    
    private void testDifferentConfigurations() throws Exception {
        System.out.println("\n=== 测试不同配置参数 ===");
        
        // 测试不同的缓冲区大小
        int[] bufferSizes = {8192, 16384, 32768, 65536};
        for (int bufferSize : bufferSizes) {
            testWithBufferSize(bufferSize);
        }
        
        // 测试直接COPY vs 管道COPY
        testDirectVsPiped();
    }
    
    private void testWithBufferSize(int bufferSize) throws Exception {
        System.out.println("\n--- 测试缓冲区大小: " + bufferSize + " bytes ---");
        
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            CopyManager copyManager = conn.unwrap(PGConnection.class).getCopyAPI();
            
            JdbcSourceConfig config = JdbcSourceConfig.builder()
                .copyFormat("CSV_OPTIMIZED")
                .copyBufferSize(bufferSize)
                .useDirectCopy(true)
                .pipeSize(50 * 1024 * 1024)
                .build();
            
            SeaTunnelRowType rowType = createTestRowType();
            PostgresDialect dialect = new PostgresDialect();
            String copySql = dialect.getCopySelectSql(TEST_QUERY, PostgresDialect.CopyFormat.CSV_OPTIMIZED);
            
            long startTime = System.currentTimeMillis();
            AtomicLong recordCount = new AtomicLong(0);
            
            try (OptimizedCopyReader reader = new OptimizedCopyReader(copyManager, copySql, rowType, config)) {
                reader.open();
                
                while (reader.hasNext()) {
                    SeaTunnelRow row = reader.nextRecord();
                    if (row != null) {
                        recordCount.incrementAndGet();
                    }
                }
            }
            
            long duration = System.currentTimeMillis() - startTime;
            double recordsPerSecond = (double) recordCount.get() / (duration / 1000.0);
            
            System.out.println("  处理速度: " + String.format("%.2f", recordsPerSecond) + " 记录/秒");
        }
    }
    
    private void testDirectVsPiped() throws Exception {
        System.out.println("\n--- 测试直接COPY vs 管道COPY ---");
        
        // 测试直接COPY
        testCopyMode(true, "直接COPY");
        
        // 测试管道COPY
        testCopyMode(false, "管道COPY");
    }
    
    private void testCopyMode(boolean useDirectCopy, String description) throws Exception {
        System.out.println("\n" + description + ":");
        
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            CopyManager copyManager = conn.unwrap(PGConnection.class).getCopyAPI();
            
            JdbcSourceConfig config = JdbcSourceConfig.builder()
                .copyFormat("CSV_OPTIMIZED")
                .copyBufferSize(16384)
                .useDirectCopy(useDirectCopy)
                .pipeSize(50 * 1024 * 1024)
                .build();
            
            SeaTunnelRowType rowType = createTestRowType();
            PostgresDialect dialect = new PostgresDialect();
            String copySql = dialect.getCopySelectSql(TEST_QUERY, PostgresDialect.CopyFormat.CSV_OPTIMIZED);
            
            long startTime = System.currentTimeMillis();
            AtomicLong recordCount = new AtomicLong(0);
            
            try (OptimizedCopyReader reader = new OptimizedCopyReader(copyManager, copySql, rowType, config)) {
                reader.open();
                
                while (reader.hasNext()) {
                    SeaTunnelRow row = reader.nextRecord();
                    if (row != null) {
                        recordCount.incrementAndGet();
                    }
                }
            }
            
            long duration = System.currentTimeMillis() - startTime;
            double recordsPerSecond = (double) recordCount.get() / (duration / 1000.0);
            
            System.out.println("  处理速度: " + String.format("%.2f", recordsPerSecond) + " 记录/秒");
        }
    }
    
    private JdbcSourceConfig createTestConfig(PostgresDialect.CopyFormat format) {
        return JdbcSourceConfig.builder()
            .copyFormat(format.name())
            .copyBufferSize(16384)
            .useDirectCopy(true)
            .pipeSize(50 * 1024 * 1024)
            .build();
    }
    
    private SeaTunnelRowType createTestRowType() {
        // 创建一个示例行类型，根据实际测试表结构调整
        return new SeaTunnelRowType(
            new String[]{"id", "name", "value", "created_at"},
            new org.apache.seatunnel.api.table.type.SeaTunnelDataType[]{
                BasicType.LONG_TYPE,
                BasicType.STRING_TYPE,
                BasicType.DOUBLE_TYPE,
                BasicType.LOCAL_DATE_TIME_TYPE
            }
        );
    }
    
    private long getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        return (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024);
    }
}
