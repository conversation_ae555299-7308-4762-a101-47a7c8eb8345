/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.mergefile.s3.sink;

import com.google.auto.service.AutoService;
import org.apache.seatunnel.api.common.SeaTunnelAPIErrorCode;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.sink.*;
import org.apache.seatunnel.api.table.catalog.Catalog;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.factory.CatalogFactory;
import org.apache.seatunnel.common.config.CheckConfigUtil;
import org.apache.seatunnel.common.config.CheckResult;
import org.apache.seatunnel.common.constants.PluginType;
import org.apache.seatunnel.connectors.seatunnel.file.merge.config.FileSystemType;
import org.apache.seatunnel.connectors.seatunnel.file.merge.exception.FileConnectorException;
import org.apache.seatunnel.connectors.seatunnel.file.merge.sink.BaseMultipleTableFileSink;
import org.apache.seatunnel.connectors.seatunnel.file.mergefile.s3.config.S3Conf;
import org.apache.seatunnel.connectors.seatunnel.file.mergefile.s3.config.S3ConfigOptions;
import org.apache.seatunnel.shade.com.typesafe.config.Config;

import java.util.Optional;

import static org.apache.seatunnel.api.table.factory.FactoryUtil.discoverFactory;

@AutoService(SeaTunnelSink.class)
public class S3FileSink extends BaseMultipleTableFileSink implements SupportSaveMode {

    private CatalogTable catalogTable;
    private ReadonlyConfig readonlyConfig;

    private static final String S3 = "S3";

    @Override
    public String getPluginName() {
        return FileSystemType.MERGES3.getFileSystemPluginName();
    }

    public S3FileSink(CatalogTable catalogTable, ReadonlyConfig readonlyConfig) {
        super(S3Conf.buildWithConfig(readonlyConfig.toConfig()), readonlyConfig, catalogTable);
        this.catalogTable = catalogTable;
        this.readonlyConfig = readonlyConfig;
        Config pluginConfig = readonlyConfig.toConfig();
        CheckResult result =
                CheckConfigUtil.checkAllExists(
                        pluginConfig,
                        S3ConfigOptions.FILE_PATH.key(),
                        S3ConfigOptions.S3_BUCKET.key());
        if (!result.isSuccess()) {
            throw new FileConnectorException(
                    SeaTunnelAPIErrorCode.CONFIG_VALIDATION_FAILED,
                    String.format(
                            "PluginName: %s, PluginType: %s, Message: %s",
                            getPluginName(), PluginType.SINK, result.getMsg()));
        }
    }

    @Override
    public Optional<SaveModeHandler> getSaveModeHandler() {

        CatalogFactory catalogFactory =
                discoverFactory(
                        Thread.currentThread().getContextClassLoader(), CatalogFactory.class, S3);
        if (catalogFactory == null) {
            return Optional.empty();
        }
        final Catalog catalog = catalogFactory.createCatalog(S3, readonlyConfig);
        SchemaSaveMode schemaSaveMode = readonlyConfig.get(S3ConfigOptions.SCHEMA_SAVE_MODE);
        DataSaveMode dataSaveMode = readonlyConfig.get(S3ConfigOptions.DATA_SAVE_MODE);
        return Optional.of(
                new DefaultSaveModeHandler(
                        schemaSaveMode, dataSaveMode, catalog, catalogTable, null));
    }
}
