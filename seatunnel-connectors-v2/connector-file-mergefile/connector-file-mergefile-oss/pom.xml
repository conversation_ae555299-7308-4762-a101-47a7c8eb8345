<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>connector-file</artifactId>
        <version>2.3.4</version>
    </parent>

    <artifactId>connector-file-mergefile-oss</artifactId>
    <name>SeaTunnel : Connectors V2 : MergeFile : Oss</name>

    <properties>
        <aliyun.sdk.oss.version>3.4.1</aliyun.sdk.oss.version>
        <hadoop-aliyun.version>3.1.4</hadoop-aliyun.version>
        <jdom.version>1.1</jdom.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-mergefile-base</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-hadoop3-3.1.4-uber</artifactId>
            <version>${project.version}</version>
            <classifier>optional</classifier>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.avro</groupId>
                    <artifactId>avro</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>${aliyun.sdk.oss.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jdom</groupId>
            <artifactId>jdom</artifactId>
            <version>${jdom.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-aliyun-joyadata</artifactId>
            <version>${hadoop-aliyun.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>