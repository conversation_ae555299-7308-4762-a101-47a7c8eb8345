/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sink.commit;

import org.apache.commons.lang3.StringUtils;
import org.apache.seatunnel.api.sink.SinkAggregatedCommitter;
import org.apache.seatunnel.connectors.seatunnel.file.config.HadoopConf;
import org.apache.seatunnel.connectors.seatunnel.file.hadoop.HadoopFileSystemProxy;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class FileSinkAggregatedCommitter
        implements SinkAggregatedCommitter<FileCommitInfo, FileAggregatedCommitInfo> {
    protected HadoopFileSystemProxy hadoopFileSystemProxy;

    public FileSinkAggregatedCommitter(HadoopConf hadoopConf) {
        this.hadoopFileSystemProxy = new HadoopFileSystemProxy(hadoopConf);
    }

    @Override
    public List<FileAggregatedCommitInfo> commit(
            List<FileAggregatedCommitInfo> aggregatedCommitInfos) throws IOException {
        List<FileAggregatedCommitInfo> errorAggregatedCommitInfoList = new ArrayList<>();
        List<String> files = new ArrayList<>();
        String validateFile = null;
        String validateContent = null;
        boolean isFile = false;
        String path = null;
        boolean validates = false;
        String tdsqlPartitionName = null;
        String fileNameExpression = null;
        if (null != aggregatedCommitInfos && aggregatedCommitInfos.size() > 0) {
            validateFile = aggregatedCommitInfos.get(0).getValidateFile();
            validateContent = aggregatedCommitInfos.get(0).getValidateContent();
            isFile = aggregatedCommitInfos.get(0).isFile();
            path = aggregatedCommitInfos.get(0).getPath();
            validates = aggregatedCommitInfos.get(0).isValidates();
            tdsqlPartitionName = aggregatedCommitInfos.get(0).getTdsqlPartitionName();
            fileNameExpression = aggregatedCommitInfos.get(0).getFileNameExpression();
            //打印开始重命名并删除临时文件的日志，放在非空判断中，因为发现任务在运行write写数据之前就会进来一次
            log.info("Start renaming!!!");
        }
        aggregatedCommitInfos.forEach(
                aggregatedCommitInfo -> {
                    try {
                        for (Map.Entry<String, LinkedHashMap<String, String>> entry :
                                aggregatedCommitInfo.getTransactionMap().entrySet()) {
                            for (Map.Entry<String, String> mvFileEntry :
                                    entry.getValue().entrySet()) {
                                // first rename temp file
                                hadoopFileSystemProxy.renameFile(
                                        mvFileEntry.getKey(), mvFileEntry.getValue(), true);
                                files.add(mvFileEntry.getValue());
                            }
                            // second delete transaction directory
                            hadoopFileSystemProxy.deleteFile(entry.getKey());
                        }
                    } catch (Throwable e) {
                        log.error(
                                "commit aggregatedCommitInfo error, aggregatedCommitInfo = {} ",
                                aggregatedCommitInfo,
                                e);
                        errorAggregatedCommitInfoList.add(aggregatedCommitInfo);
                    }
                });
        if (null != aggregatedCommitInfos && aggregatedCommitInfos.size() > 0 && files.size() > 0) {
            //打印重命名并删除临时文件完成的日志，放在非空判断中，因为发现任务在运行write写数据之前就会进来一次
            log.info("Rename complete!!!");
            mergeFiles(files, validateFile, validateContent, isFile, path, validates, tdsqlPartitionName, fileNameExpression);
        }
        return errorAggregatedCommitInfoList;
    }

    public void mergeFiles(List<String> filePaths, String validateFile, String validateContent, boolean isFile, String path, boolean validates, String tdsqlPartitionName, String fileNameExpression) throws IOException {
        if (StringUtils.isNotBlank(validateFile)) {
            if (!validates) {//一个flg文件
                hadoopFileSystemProxy.writeValidateFile(validateFile, validateContent, filePaths, isFile, path, tdsqlPartitionName, fileNameExpression);
            } else {//多个flg文件
                hadoopFileSystemProxy.writeValidateFiles(validateFile, validateContent, filePaths, isFile, path);
            }
        }
    }

    /**
     * The logic about how to combine commit message.
     *
     * @param commitInfos The list of commit message.
     * @return The commit message after combine.
     */
    @Override
    public FileAggregatedCommitInfo combine(List<FileCommitInfo> commitInfos) {
        if (commitInfos == null || commitInfos.size() == 0) {
            return null;
        }
        LinkedHashMap<String, LinkedHashMap<String, String>> aggregateCommitInfo =
                new LinkedHashMap<>();
        LinkedHashMap<String, List<String>> partitionDirAndValuesMap = new LinkedHashMap<>();
        commitInfos.forEach(
                commitInfo -> {
                    LinkedHashMap<String, String> needMoveFileMap =
                            aggregateCommitInfo.computeIfAbsent(
                                    commitInfo.getTransactionDir(), k -> new LinkedHashMap<>());
                    needMoveFileMap.putAll(commitInfo.getNeedMoveFiles());
                    if (commitInfo.getPartitionDirAndValuesMap() != null
                            && !commitInfo.getPartitionDirAndValuesMap().isEmpty()) {
                        partitionDirAndValuesMap.putAll(commitInfo.getPartitionDirAndValuesMap());
                    }
                });
        return new FileAggregatedCommitInfo(aggregateCommitInfo, partitionDirAndValuesMap, commitInfos.get(0).getValidateFile(), commitInfos.get(0).getValidateContent(),
                commitInfos.get(0).isFile(), commitInfos.get(0).getPath(), commitInfos.get(0).isValidates(), commitInfos.get(0).getTdsqlPartitionName(), commitInfos.get(0).getFileNameExpression());
    }

    /**
     * If {@link #commit(List)} failed, this method will be called (**Only** on Spark engine at
     * now).
     *
     * @param aggregatedCommitInfos The list of combine commit message.
     * @throws Exception throw Exception when abort failed.
     */
    @Override
    public void abort(List<FileAggregatedCommitInfo> aggregatedCommitInfos) throws Exception {
        log.info("rollback aggregate commit");
        if (aggregatedCommitInfos == null || aggregatedCommitInfos.size() == 0) {
            return;
        }
        aggregatedCommitInfos.forEach(
                aggregatedCommitInfo -> {
                    try {
                        for (Map.Entry<String, LinkedHashMap<String, String>> entry :
                                aggregatedCommitInfo.getTransactionMap().entrySet()) {
                            // rollback the file
                            for (Map.Entry<String, String> mvFileEntry :
                                    entry.getValue().entrySet()) {
                                if (hadoopFileSystemProxy.fileExist(mvFileEntry.getValue())
                                        && !hadoopFileSystemProxy.fileExist(mvFileEntry.getKey())) {
                                    hadoopFileSystemProxy.renameFile(
                                            mvFileEntry.getValue(), mvFileEntry.getKey(), true);
                                }
                            }
                            // delete the transaction dir
                            hadoopFileSystemProxy.deleteFile(entry.getKey());
                        }
                    } catch (Exception e) {
                        log.error("abort aggregatedCommitInfo error ", e);
                    }
                });
    }

    /**
     * Close this resource.
     *
     * @throws IOException throw IOException when close failed.
     */
    @Override
    public void close() throws IOException {
        hadoopFileSystemProxy.close();
        //hadoopFileSystemProxy.getFileSystem().close();
    }
}
