/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2019. All rights reserved.
 */

package org.apache.seatunnel.connectors.seatunnel.file.hadoop.kerberos;

import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * KerberoUtil
 *
 * <AUTHOR> @since 8.0.0
 */
@Slf4j
public class HuaweiKerberosUtil {
    private static final Logger logger = LoggerFactory.getLogger(HuaweiKerberosUtil.class);
    /**
     * JAVA_VENDER
     */
    public static final String JAVA_VENDER = "java.vendor";
    /**
     * IBM_FLAG
     */
    public static final String IBM_FLAG = "IBM";
    /**
     * CONFIG_CLASS_FOR_IBM
     */
    public static final String CONFIG_CLASS_FOR_IBM = "com.ibm.security.krb5.internal.Config";
    /**
     * CONFIG_CLASS_FOR_SUN
     */
    public static final String CONFIG_CLASS_FOR_SUN = "sun.security.krb5.Config";
    /**
     * METHOD_GET_INSTANCE
     */
    public static final String METHOD_GET_INSTANCE = "getInstance";
    /**
     * METHOD_GET_DEFAULT_REALM
     */
    public static final String METHOD_GET_DEFAULT_REALM = "getDefaultRealm";
    /**
     * DEFAULT_REALM
     */
    public static final String DEFAULT_REALM = "HADOOP.COM";


    private static final String ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME = "Client";
    private static final String ZOOKEEPER_SERVER_PRINCIPAL_KEY = "zookeeper.server.principal";
    private static final String ZOOKEEPER_DEFAULT_SERVER_PRINCIPAL = "zookeeper/hadoop";

    private static Configuration CONF = null;
    private static String KRB5_FILE = null;
    private static String USER_KEYTAB_FILE = null;

    /* zookeeper节点ip和端口列表 */
    private static String auth = null;

    /**
     * Get Krb5 Domain Realm
     */
    public static String getKrb5DomainRealm() {
        Class<?> krb5ConfClass;
        String peerRealm = null;
        try {
            if (System.getProperty(JAVA_VENDER).contains(IBM_FLAG)) {
                krb5ConfClass = Class.forName(CONFIG_CLASS_FOR_IBM);
            } else {
                krb5ConfClass = Class.forName(CONFIG_CLASS_FOR_SUN);
            }

            Method getInstanceMethod = krb5ConfClass.getMethod(METHOD_GET_INSTANCE);
            Object kerbConf = getInstanceMethod.invoke(krb5ConfClass);

            Method getDefaultRealmMethod = krb5ConfClass.getDeclaredMethod(METHOD_GET_DEFAULT_REALM);
            if (getDefaultRealmMethod.invoke(kerbConf) instanceof String) {
                peerRealm = (String) getDefaultRealmMethod.invoke(kerbConf);
            }
            logger.info("Get default realm successfully, the realm is : {}", peerRealm);

        } catch (ClassNotFoundException e) {
            peerRealm = DEFAULT_REALM;
            logger.warn("Get default realm failed, use default value : " + DEFAULT_REALM);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            peerRealm = DEFAULT_REALM;
            logger.warn("Get default realm failed, use default value : " + DEFAULT_REALM);
        }

        return peerRealm;
    }

    public static void init(String krbFilePath, String user,String userKeytabPath) {
        try {
            CONF = new Configuration();
            //CONF.addResource(new Path(krbPath + "core-site.xml"));
            //CONF.addResource(new Path(krbPath + "hive-site.xml"));

            //KRB5_FILE = krbPath + "krb5.conf";
            //USER_KEYTAB_FILE = krbPath + "user.keytab";
            KRB5_FILE = krbFilePath;
            USER_KEYTAB_FILE = userKeytabPath;
            log.info("开始设置jass文件...");
            LoginUtil.setJaasConf(ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME, user, USER_KEYTAB_FILE);
            log.info("jaas文件设置完毕，开始设置zkPrincipal");
            //LoginUtil.setZookeeperServerPrincipal(ZOOKEEPER_SERVER_PRINCIPAL_KEY, ZOOKEEPER_DEFAULT_SERVER_PRINCIPAL);
            //log.info("zkPrincipal设置完毕，开始登陆");
            // 安全模式
            // Zookeeper登录认证
            LoginUtil.login(user, USER_KEYTAB_FILE, KRB5_FILE, CONF);
            log.info("认证完毕。。");
            //zookeeper开启ssl时需要设置JVM参数
            //LoginUtil.processZkSsl(clientInfo);
            //log.info("zk ssl开启完毕");
        } catch (Exception e) {
            log.info("华为kerberos认证失败，原因是{}", e.getMessage());
            e.printStackTrace();
        }
    }

    public static String getFilePath(String krb5) {
        int index = krb5.lastIndexOf("/");
        if (index > 0) {
            return krb5.substring(0, index + 1);
        }
        return null;
    }
    
}
