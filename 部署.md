mvn spotless:apply
本地构建
mvn clean install -Dmaven.test.skip=true -T 4
打包
mvn clean package install -pl seatunnel-dist -am -Dmaven.test.skip=true

seatunnel.yaml
seatunnel:
engine:
history-job-expire-minutes: 1440
backup-count: 1
queue-type: blockingqueue
print-execution-info-interval: 10
print-job-metrics-info-interval: 10
slot-service:
dynamic-slot: true
checkpoint:
interval: 30000
timeout: 600000
storage:
type: hdfs
max-retained: 3
plugin-config:
namespace: /tmp/seatunnel/checkpoint_snapshot
storage.type: hdfs
fs.defaultFS: file:/// 为本地路径存储

验证
启动zeta引擎
./seatunnel-cluster.sh -d
启动同步任务
./seatunnel.sh --config ./../config/001.conf -e cluster
暂停同步任务
./seatunnel.sh -s 779223181985579009
恢复运行同步任务
./seatunnel.sh --config ./../config/001.conf -r 779223181985579009
取消任务
./seatunnel.sh -can 779223181985579009

使用multi_source_to_multi_sink1.conf 运行的时候，一个jdbc就是一个连接。读写是分开的。
使用multi_source_to_multi_sink2.conf 运行的时候

cdc同步目前要求源端必须有主键

./seatunnel.sh --config ./../config/lihj.conf -e cluster

写到hive，基于cdh
参考https://blog.csdn.net/qq_43224174/article/details/131430223
从cdh机器上/opt/cloudera/parcels/CDH/lib/hive/lib 路径copy文件
-rw-r--r--. 1 <USER> <GROUP> 438744 2月 3 00:49 hive-common-2.1.1-cdh6.3.2.jar
-rw-r--r--. 1 <USER> <GROUP> 438744 2月 3 00:49 hive-common.jar
-rw-r--r--. 1 <USER> <GROUP> 35803898 2月 3 00:16 hive-exec-2.1.1-cdh6.3.2.jar
-rw-r--r--. 1 <USER> <GROUP> 35803898 2月 3 00:50 hive-exec.jar
-rw-r--r--. 1 <USER> <GROUP> 8251305 2月 3 00:37 hive-metastore-2.1.1-cdh6.3.2.jar
-rw-r--r--. 1 <USER> <GROUP> 8251305 2月 3 00:48 hive-metastore.jar
-rw-r--r--. 1 <USER> <GROUP> 313702 2月 3 00:45 libfb303-0.9.3.jar
-rw-r--r--. 1 <USER> <GROUP> 234121 2月 3 00:49 libthrift-0.9.3-1.jar
copy到seatunnel的lib下，然后重启zeta引擎

说明
D:\git_seatunnel\dev_seatunnel\seatunnel 跟随社区进度2.3.4做测试验证
D:\open_git\open_st\seatunnel 社区贡献

postgresqlcdc开启

```shell
postgresql cdc 需要在配置文件postgresql.conf 修改

#------------------------------------------------------------------------------
# WRITE AHEAD LOG
#------------------------------------------------------------------------------

# - Settings -

wal_level = logical                     # minimal, replica, or logical  主要改这里，改为logical pg就可以读取到cdc内容了
                                        # (change requires restart)
#fsync = on                             # flush data to disk for crash safety
                                        # (turning this off can cause
                                        # unrecoverable data corruption)
#synchronous_commit = on                # synchronization level;
                                        # off, local, remote_write, remote_apply, or on
#wal_sync_method = fsync                # the default is the first option

```

oracle cdc

```shell
查看表空间文件位置
select t1.name,t2.name  from v$tablespace t1,v$datafile t2 where t1.ts# = t2.ts#


CREATE TABLESPACE logminer_tbs DATAFILE '/opt/oracle/oradata/ORCLCDB/ORCLPDB1/logminer_tbs.dbf' SIZE 25M REUSE AUTOEXTEND ON MAXSIZE UNLIMITED;
CREATE USER joyadata IDENTIFIED BY joyadata DEFAULT TABLESPACE LOGMINER_TBS QUOTA UNLIMITED ON LOGMINER_TBS;
-- 允许"joyadata"用户创建会话，即允许该用户连接到数据库。
GRANT CREATE SESSION TO joyadata;
-- （不支持Oracle 11g）允许"joyadata"用户在多租户数据库（CDB）中设置容器。
-- GRANT SET CONTAINER TO joyadata;
-- 允许"joyadata"用户查询V_$DATABASE视图，该视图包含有关数据库实例的信息。
GRANT SELECT ON V_$DATABASE TO joyadata;
-- 允许"joyadata"用户执行任何表的闪回操作。
GRANT FLASHBACK ANY TABLE TO joyadata;
-- 允许"joyadata"用户查询任何表的数据。
GRANT SELECT ANY TABLE TO joyadata;
-- 允许"joyadata"用户拥有SELECT_CATALOG_ROLE角色，该角色允许查询数据字典和元数据。
GRANT SELECT_CATALOG_ROLE TO joyadata;
-- 允许"joyadata"用户拥有EXECUTE_CATALOG_ROLE角色，该角色允许执行一些数据字典中的过程和函数。
GRANT EXECUTE_CATALOG_ROLE TO joyadata;
-- 允许"joyadata"用户查询任何事务。
GRANT SELECT ANY TRANSACTION TO joyadata;
-- （不支持Oracle 11g）允许"joyadata"用户进行数据变更追踪（LogMiner）。
-- GRANT LOGMINING TO joyadata;
-- 允许"joyadata"用户创建表。
GRANT CREATE TABLE TO joyadata;
-- 允许"joyadata"用户锁定任何表。
GRANT LOCK ANY TABLE TO joyadata;
-- 允许"joyadata"用户修改任何表。
GRANT ALTER ANY TABLE TO joyadata;
-- 允许"joyadata"用户创建序列。
GRANT CREATE SEQUENCE TO joyadata;
-- 允许"joyadata"用户执行DBMS_LOGMNR包中的过程。
GRANT EXECUTE ON DBMS_LOGMNR TO joyadata;
-- 允许"joyadata"用户执行DBMS_LOGMNR_D包中的过程。
GRANT EXECUTE ON DBMS_LOGMNR_D TO joyadata;
-- 允许"joyadata"用户查询V_$LOG视图，该视图包含有关数据库日志文件的信息。
GRANT SELECT ON V_$LOG TO joyadata;
-- 允许"joyadata"用户查询V_$LOG_HISTORY视图，该视图包含有关数据库历史日志文件的信息。
GRANT SELECT ON V_$LOG_HISTORY TO joyadata;
-- 允许"joyadata"用户查询V_$LOGMNR_LOGS视图，该视图包含有关LogMiner日志文件的信息。
GRANT SELECT ON V_$LOGMNR_LOGS TO joyadata;
-- 允许"joyadata"用户查询V_$LOGMNR_CONTENTS视图，该视图包含LogMiner日志文件的内容。
GRANT SELECT ON V_$LOGMNR_CONTENTS TO joyadata;
-- 允许"joyadata"用户查询V_$LOGMNR_PARAMETERS视图，该视图包含有关LogMiner的参数信息。
GRANT SELECT ON V_$LOGMNR_PARAMETERS TO joyadata;
-- 允许"joyadata"用户查询V_$LOGFILE视图，该视图包含有关数据库日志文件的信息。
GRANT SELECT ON V_$LOGFILE TO joyadata;
-- 允许"joyadata"用户查询V_$ARCHIVED_LOG视图，该视图包含已归档的数据库日志文件的信息。
GRANT SELECT ON V_$ARCHIVED_LOG TO joyadata;
-- 允许"joyadata"用户查询V_$ARCHIVE_DEST_STATUS视图，该视图包含有关归档目标状态的信息。
GRANT SELECT ON V_$ARCHIVE_DEST_STATUS TO joyadata;
ALTER DATABASE ADD SUPPLEMENTAL LOG DATA;
ALTER TABLE JOYADATA.EMP_104 ADD SUPPLEMENTAL LOG DATA (ALL) COLUMNS;
```

sqlserver cdc

./seatunnel.sh --config ./../config/mysql2dm.conf -e cluster
./seatunnel.sh --config ./../config/mysql2mysql.conf -e cluster
./seatunnel.sh --config ./../config/mysql2oracle.conf -e cluster
./seatunnel.sh --config ./../config/mysql2postgresql.conf -e cluster
./seatunnel.sh --config ./../config/mysql2sqlserver.conf -e cluster

./seatunnel.sh --config ./../config/cdc/mysql/mysqlcdc.conf -e cluster
./seatunnel.sh --config ./../config/cdc/oracle/00001.conf -e cluster xx
./seatunnel.sh --config ./../config/cdc/postgresql/postgres_cdc_to_mysql.conf -e cluster xx
./seatunnel.sh --config ./../config/cdc/sqlserver/sqlservercdc_to_mysql.conf -e cluster
./seatunnel.sh --config ./../config/es2hive.conf -e cluster

./seatunnel.sh --config 这里是你的conf路径 -e cluster

./seatunnel.sh --config ./../config/mysql2es.conf -e cluster

./seatunnel.sh --config ./../config/m2o.conf -e cluster

sql函数可以写select * from xxx
但是表名称需要写虚拟表（result_table_name）

./seatunnel-cluster.sh -d

argo 建表
SET transaction.type=inceptor;
CREATE TABLE emp_user (id INT,name STRING,address STRING)
CLUSTERED BY (id) INTO 2 BUCKETS STORED AS ORC
TBLPROPERTIES ("transactional"="true");

hazelcast集群配置
https://mp.weixin.qq.com/s/HtK1zUp_KHuJ79KP8rCQ0A

hive 建表
CREATE TABLE `default.lihj_test1`(
`vin` varchar(100),
`brand` varchar(100),
`ym` varchar(100),
`mileage` varchar(100),
`mn` varchar(100))
ROW FORMAT SERDE
'org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe'
WITH SERDEPROPERTIES (
'field.delim'='\t',
'serialization.format'='\t')
STORED AS INPUTFORMAT
'org.apache.hadoop.mapred.TextInputFormat'
OUTPUTFORMAT
'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat'
LOCATION
'hdfs://foton1.cdh.com:8020/user/hive/warehouse/lihj_test1'
TBLPROPERTIES (
'transient_lastDdlTime'='**********')

星环tdh
http://************:8180/#/login  admin/admin
beeline -u ************************ -n hive -p 123456
root/DsgData@321

arogofile

CREATE EXTERNAL TABLE emp_lihj ( emp_id STRING, emp_name STRING, gender STRING,account STRING,org_id STRING,birth_date
STRING,age STRING,nationality STRING,province STRING,email STRING,phone STRING,begin_date STRING,remark
STRING,create_time STRING,update_time STRING)
ROW FORMAT DELIMITED FIELDS TERMINATED BY ',' LOCATION '/tmp/lihj';

CREATE EXTERNAL TABLE emp_20240606 (emp_id INT,
emp_name STRING,
gender STRING,
account STRING,
org_id STRING,
birth_date STRING,
age INT,
nationality STRING,
province STRING,
city STRING,
email STRING,
phone STRING,
begin_date DATE,
remark STRING,
create_time TIMESTAMP,
update_time TIMESTAMP )

create table holo_a(id int,name string) stored as holodesk;

Doris 建表语句
CREATE TABLE customer ( c_custkey INT,
c_name VARCHAR ( 26 ),
c_city VARCHAR ( 11 ) )
DUPLICATE KEY ( c_custkey )
DISTRIBUTED BY HASH ( c_custkey ) BUCKETS 10 PROPERTIES ( "replication_num" = "1" );

工银瑞信json
{"agent_send_timestamp":**********250,"collector_recv_timestamp":**********658,"raw_message":"2024-05-30-14:14:
27.**********,,*************,,switch,,huawei,,*******.********,,ARP表3)","ip":"***********","index":"
ops-yotta-********","logical_index":"yotta","logtype":"other","hostname":"SPLUNK230606K00T","appname":"snmp","domain":"
ops","context_id":**********"raw_message_length":148,"timestamp":**********250}
{"_index":"yotta","raw_message":"<188>Jun 03 2024 10:56:26 JSZX-Huiju %%01SNMP/4/SNMP_IPLOCK(s)[449]:The source IP was
locked because of the failure of login through SNMP.(SourceIuawei.log","switch.sw_name":"JSZX-Huiju","hostname":"
VM_16_9_centos","logtype":"switch","appname":"switch","switch.count":449,"switch.pri":"188","host":"***********","
switch.kvmsgp":1717383386000,"agent_send_timestamp":1717383392400,"switch.severity":4,"collector_recv_timestamp":
1717383393098,"ip":"***********","switch.describe":"警告可能存在某种差错","swiule":"SNMP","context_id":
1717383392400319046,"_id":"AY_cBuyQKQXXgHkGUehZ","switch.logtag":"s","ip_addr":"***********","event_time":1717383386000}


sqlserver建库时需要指定排序规则，否则varchar类型字段中有中文时会乱码，示例：
CREATE DATABASE zsp  
COLLATE Chinese_PRC_CI_AS;

impala 建表语句
CREATE TABLE IF NOT EXISTS default.emp_02 (
emp_id INT,                     
emp_name STRING
)      

-Xms42g -Xmx42g -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/seatunnel/dump/zeta-server -XX:MaxMetaspaceSize=8g -XX:+UseG1GC -XX:+PrintGCDetails -Xloggc:/alidata1/za-seatunnel/logs/gc.log -XX:+PrintGCDateStamps -XX:MaxGCPauseMillis=5000 -XX:InitiatingHeapOccupancyPercent=50 -XX:+UseStringDeduplication -XX:GCTimeRatio=4 -XX:G1ReservePercent=15 -XX:ConcGCThreads=12 -XX:G1HeapRegionSize=32M
16c/64G

doris 建表语句

```sql
CREATE TABLE IF NOT EXISTS test.table_partition_range (
     id BIGINT COMMENT 'id'
    ,name STRING COMMENT '姓名'
    ,gender STRING COMMENT '性别'
    ,age INT COMMENT '年龄'
    ,hero STRING COMMENT '英雄角色'
    ,hero_skill_damage_score DOUBLE COMMENT '英雄技能伤害评分'
    ,partition_value DATE COMMENT '分区值(指定列)'
)
    -- ENGINE=OLAP -- 不指定(默认OLAP)
/* DUPLICATE KEY(`id`) */ -- 默认为明细模型(排序列系统自动选定了前 3 列)
    COMMENT '分区表_range分区'
    PARTITION BY RANGE(partition_value) () /* 本次演示不预建分区 */
    DISTRIBUTED BY HASH(id) BUCKETS AUTO /* 自动分桶 */
    PROPERTIES (
                   "replication_num" = "1",
    -- 设置: 无排序列的默认明细模型
                   "enable_duplicate_without_keys_by_default" = "true"
    -- 使用自动分桶推算的创建语法(该参数可选, 如果不设置默认 "estimate_partition_size" = "10G")
    ,"estimate_partition_size" = "2G"
               )
查看有哪些分区
SHOW PARTITIONS FROM sales_records;
```

oracle类型的数据库 报错这个:	
Caused by: org.apache.seatunnel.engine.common.exception.SeaTunnelEngineException: java.lang.SecurityException: sealing violation: package oracle.jdbc.driver is sealed
在seatunnel-cluster.sh 配置文件 nohup java 后加参数-Dcom.sun.jndi.rmi.securirty.sealing=false

使用oracle9i时需要将lib中的ali-phoenix-shaded-thin-client-5.2.5-HBase-2.x.jar删掉
需要用oracle-10.2.0.4.jar包
tbase 底层可以使用csv copy方式写入

写文件的文件名称结构，例如：test_0_1_0.txt，
后缀_0_1_0.txt
第一位0是线程id
第二位1是checkpointId
第三位0是当前线程写的第几个文件，从0开始